package douyin

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/valyala/fasthttp"
)

// DyHttpClient 抽象出来的HTTP客户端，用于与抖音API通信
type DyHttpClient struct {
	BaseURL string
	Client  *fasthttp.Client
}

// NewDyHttpClient 创建一个新的抖音HTTP客户端
func NewDyHttpClient(baseURL string) *DyHttpClient {
	return &DyHttpClient{
		BaseURL: baseURL,
		Client: &fasthttp.Client{
			ReadTimeout:         60 * time.Second,  // 增加读取超时时间
			WriteTimeout:        30 * time.Second,  // 增加写入超时时间
			MaxIdleConnDuration: 300 * time.Second, // 增加空闲连接持续时间到5分钟
			MaxConnDuration:     600 * time.Second, // 增加连接最大持续时间到10分钟
			MaxConnsPerHost:     200,               // 增加每个主机的最大连接数
			Dial: (&fasthttp.TCPDialer{
				Concurrency:      1000,
				DNSCacheDuration: time.Hour,
			}).Dial,
		},
	}
}

// DoRequest 发送HTTP请求的通用方法
func (c *DyHttpClient) DoRequest(method, path string, reqBody map[string]any, respBody interface{}) error {
	if reqBody != nil && reqBody["proxy"] == "" {
		reqBody["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	const maxRetries = 3
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			time.Sleep(time.Duration(i*2) * time.Second) // 增加重试延迟
		}

		jsonBody, err := json.Marshal(reqBody)
		if err != nil {
			return fmt.Errorf("marshal request body failed: %w", err)
		}

		req := fasthttp.AcquireRequest()
		resp := fasthttp.AcquireResponse()
		defer fasthttp.ReleaseRequest(req)
		defer fasthttp.ReleaseResponse(resp)

		req.SetRequestURI(fmt.Sprintf("%s%s", c.BaseURL, path))
		req.Header.SetMethod(method)
		req.Header.SetContentType("application/json")

		// 启用Keep-Alive连接
		req.Header.Set("Connection", "keep-alive")
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

		req.SetBody(jsonBody)

		// 记录请求开始时间
		requestStartTime := time.Now()
		global.GVA_LOG.Info(fmt.Sprintf("开始发送请求到 %s", path))

		// 设置更长的超时时间
		err = c.Client.DoTimeout(req, resp, 60*time.Second)

		// 记录请求结束时间和耗时
		requestDuration := time.Since(requestStartTime)
		global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))

		if err != nil {
			lastErr = fmt.Errorf("request attempt %d failed: %w", i+1, err)
			global.GVA_LOG.Error(fmt.Sprintf("请求失败，正在重试 (%d/%d): %v", i+1, maxRetries, err))
			continue
		}

		if resp.StatusCode() != fasthttp.StatusOK {
			// 尝试解析响应体，看是否包含error字段
			var errorResp struct {
				Error string `json:"error"`
			}

			respBody := string(resp.Body())
			decodedBody := respBody

			// 尝试解析JSON
			if err := json.Unmarshal(resp.Body(), &errorResp); err == nil && errorResp.Error != "" {
				// 如果成功解析并且有error字段，进行URL解码
				decodedError, _ := url.QueryUnescape(errorResp.Error)
				decodedBody = fmt.Sprintf(`{"error": "%s"}`, decodedError)
			}

			lastErr = fmt.Errorf("request failed with status code: %d, body: %s",
				resp.StatusCode(), respBody)
			global.GVA_LOG.Error(fmt.Sprintf("请求返回非200状态码，正在重试 (%d/%d): %v, 解码后: %s",
				i+1, maxRetries, lastErr, decodedBody))
			continue
		}

		// 先尝试解析响应
		var rawResp struct {
			Code int         `json:"code"`
			Data interface{} `json:"data"`
			Msg  string      `json:"msg"`
		}
		if err := json.Unmarshal(resp.Body(), &rawResp); err != nil {
			return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
		}

		// 如果返回错误码,解码错误信息并返回
		if rawResp.Code != 0 {
			decodedMsg, _ := url.QueryUnescape(rawResp.Msg)
			return fmt.Errorf("api error: code=%d, msg=%s", rawResp.Code, decodedMsg)
		}

		// 解析实际响应数据
		if err := json.Unmarshal(resp.Body(), respBody); err != nil {
			return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
		}

		global.GVA_LOG.Info(fmt.Sprintf("请求 %s 成功处理", path))
		return nil // 请求成功，直接返回
	}

	return fmt.Errorf("all retry attempts failed, last error: %w", lastErr)
}

// 发起表单请求
func (c *DyHttpClient) DoFormRequest(method, path string, formData map[string]string, respBody any) (err error) {
	if formData != nil && formData["proxy"] == "" {
		formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	url := fmt.Sprintf("%s%s", c.BaseURL, path)
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)
	req.Header.SetMethod(method)
	// 启用Keep-Alive连接
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.SetRequestURI(url)
	// 创建multipart writer
	var body bytes.Buffer
	writer := multipart.NewWriter(&body)
	for k, v := range formData {
		err = writer.WriteField(k, v)
		if err != nil {
			return fmt.Errorf("error writing field %s: %s -> %v", k, v, err)
		}
	}
	// 关闭writer以完成multipart构造
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("error closing writer: %v", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.SetBody(body.Bytes())

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	// 使用 c.Client 并设置20秒超时
	timeoutSec := 20
	if _, ok := formData["timeoutSec"]; ok {
		timeoutSec, err = strconv.Atoi(formData["timeoutSec"])
	}
	if err = c.Client.DoTimeout(req, resp, time.Duration(timeoutSec)*time.Second); err != nil {
		return fmt.Errorf("url: %s 请求失败 %v", url, err)
	}

	err = json.Unmarshal(resp.Body(), respBody)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}
	return nil
}

// 发送HTTP请求(JSON格式)
func (c *DyHttpClient) DoJsonRequest(method, path string, reqBody map[string]any, respBody interface{}) error {
	if reqBody != nil && reqBody["proxy"] == "" {
		reqBody["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %w", err)
	}
	req := fasthttp.AcquireRequest()
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseRequest(req)
	defer fasthttp.ReleaseResponse(resp)

	req.SetRequestURI(fmt.Sprintf("%s%s", c.BaseURL, path))
	req.Header.SetMethod(method)
	req.Header.SetContentType("application/json")

	// 启用Keep-Alive连接
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	req.SetBody(jsonBody)

	// 记录请求开始时间
	requestStartTime := time.Now()

	err = c.Client.DoTimeout(req, resp, 60*time.Second)
	if err != nil {
		return fmt.Errorf("url: %s 请求失败 %v", path, err)
	}

	// 记录请求结束时间和耗时
	requestDuration := time.Since(requestStartTime)
	global.GVA_LOG.Info(fmt.Sprintf("请求完成，耗时: %v", requestDuration))
	// 解析实际响应数据
	if err := json.Unmarshal(resp.Body(), respBody); err != nil {
		return fmt.Errorf("unmarshal response body failed: %w, body: %s", err, string(resp.Body()))
	}
	return nil // 请求成功，直接返回
}

// DoMultipartRequest 发送multipart/form-data请求，支持文件上传
func (c *DyHttpClient) DoMultipartRequest(
	method, path string,
	formData map[string]string,
	files map[string]string,
	remoteUrlMap map[string]string,
	respBody interface{},
) error {
	if formData != nil && formData["proxy"] == "" {
		formData["proxy"] = global.GVA_CONFIG.Kuaidaili.TunnelURL
	}
	url := fmt.Sprintf("%s%s", c.BaseURL, path)
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.Header.SetMethod(method)
	req.SetRequestURI(url)
	// 启用Keep-Alive连接
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 创建multipart writer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加普通表单字段
	for k, v := range formData {
		if err := writer.WriteField(k, v); err != nil {
			return fmt.Errorf("写入表单字段失败 %s: %v", k, err)
		}
	}

	if remoteUrlMap != nil {
		// 获取当前工作目录
		tempDir := filepath.Join(global.GVA_CONFIG.Local.Path, "post_temp")
		if err := utils.CreateDir(tempDir); err != nil {
			return fmt.Errorf("创建临时目录失败: %v", err)
		}
		for k, remoteURL := range remoteUrlMap {
			// 2. 生成文件名: 时间戳+随机字符串
			timestamp := time.Now().Unix()
			fileName := fmt.Sprintf("%d_%s", timestamp, filepath.Base(remoteURL))

			// 3. 下载远程文件
			filePath := filepath.Join(tempDir, fileName)
			resp, err := http.Get(remoteURL)
			if err != nil {
				return fmt.Errorf("下载远程文件失败: %v", err)
			}
			defer resp.Body.Close()

			out, err := os.Create(filePath)
			if err != nil {
				return fmt.Errorf("创建本地文件失败: %v", err)
			}
			defer out.Close()

			_, err = io.Copy(out, resp.Body)
			if err != nil {
				return fmt.Errorf("保存文件内容失败: %v", err)
			}
			defer os.Remove(filePath)

			if files == nil {
				files = make(map[string]string)
			}
			files[k] = filePath
		}
	}

	// 添加文件
	for fieldName, filePath := range files {
		file, err := os.Open(filePath)
		if err != nil {
			return fmt.Errorf("打开文件失败 %s: %v", filePath, err)
		}
		defer file.Close()

		part, err := writer.CreateFormFile(fieldName, filepath.Base(filePath))
		if err != nil {
			return fmt.Errorf("创建文件表单失败: %v", err)
		}

		if _, err := io.Copy(part, file); err != nil {
			return fmt.Errorf("复制文件内容失败: %v", err)
		}
	}

	// 关闭writer
	if err := writer.Close(); err != nil {
		return fmt.Errorf("关闭writer失败: %v", err)
	}

	req.Header.SetContentType(writer.FormDataContentType())
	req.SetBody(body.Bytes())

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := c.Client.DoTimeout(req, resp, 60*time.Second); err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}

	if err := json.Unmarshal(resp.Body(), respBody); err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	return nil
}
