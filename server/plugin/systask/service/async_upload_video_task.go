package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	"github.com/flipped-aurora/gin-vue-admin/server/model/creative"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

// 异步上传视频
type AsyncUploadVideoTask struct {
}

func (s AsyncUploadVideoTask) Exec(arg any) (err error) {
	// 获取当前时间
	now := time.Now()
	// 获取当前时间的星期几，默认星期天返回 0，将其转换为 7
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7
	}
	hour := now.Hour()
	minute := now.Minute()
	fmt.Printf("执行时间：%s:weekday:%d, hour:%d, minute:%d\n", now.Format(time.DateTime), weekday, hour, minute)

	// 1. 查询符合当前时间的待发布视频
	var publishList []struct {
		creative.AutoPublishVideo
		Nickname string
		UniqueId string
	}
	taskTitle := "异步上传视频任务"
	err = global.GVA_DB.Table("auto_publish_video").
		Select("auto_publish_video.*, dy_user.nickname as nickname, dy_user.unique_id as unique_id").
		Joins("right join dy_user on auto_publish_video.dy_user_id = dy_user.id").
		Where("dy_user.auto_publish_status = 1 AND auto_publish_video.status = 1").
		Where(
			"auto_publish_video.weekday = ? AND auto_publish_video.hour = ? AND auto_publish_video.minute = ?",
			weekday, hour, minute,
		).Find(&publishList).Error

	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 获取发布列表失败", taskTitle), zap.Error(err))
		return err
	}
	if len(publishList) == 0 {
		global.GVA_LOG.Info(fmt.Sprintf("%s,执行时间：%s:没有符合条件的任务需要执行", taskTitle, now.Format(time.DateTime)))
		return
	}

	// 遍历待发布列表
	videoLogic := logic.VideoLogic{}
	for _, item := range publishList {
		subCategoryIds := s.getSubCategoryIds(uint(item.VideoCategoryId))
		var video *creative.Video
		actualCategoryId := uint(item.VideoCategoryId) // 默认使用父分类ID

		if len(subCategoryIds) == 0 {
			// 没有子分类，则直接获取父分类的视频
			video, err = service.ServiceGroupApp.CreativeServiceGroup.GetVideoByCategoryId(uint(item.VideoCategoryId))
			if err != nil || video == nil {
				fmt.Printf("%s, 未找到对应的视频，video_category_id:%d\n", taskTitle, item.VideoCategoryId)
				continue
			}
		} else {
			global.GVA_LOG.Info(fmt.Sprintf("%s: 父分类%d有子分类，开始查找有视频的子分类", taskTitle, item.VideoCategoryId))
			// 有子分类，根据上次发布的子分类获取下一个待发布的子分类(比如A)
			// 如果子分类下没有视频，则继续找下一个子分类的，直至又回到A

			// 获取当前应该发布的子分类ID
			currentCategoryId := s.getPublishCategoryId(uint(item.VideoCategoryId), subCategoryIds)

			// 记录起始分类ID，防止无限循环
			startCategoryId := currentCategoryId

			// 尝试从当前分类开始查找有视频的分类
			for {
				video, err = service.ServiceGroupApp.CreativeServiceGroup.GetVideoByCategoryId(currentCategoryId)
				if err == nil && video != nil {
					// 找到视频，记录实际使用的分类ID并设置下一次发布的分类ID
					actualCategoryId = currentCategoryId
					s.setCurrentPublishCategoryId(uint(item.VideoCategoryId), currentCategoryId)
					break
				} else if err != nil {
					global.GVA_LOG.Error(fmt.Sprintf(
						"%s, 获取子分类视频失败, video_category_id:%d, err:%+v",
						taskTitle, currentCategoryId, err),
					)
				}

				// 当前分类没有视频，获取下一个分类
				nextCategoryId := s.getNextCategoryId(uint(item.VideoCategoryId), subCategoryIds, currentCategoryId)

				// 如果回到了起始分类，说明所有子分类都没有视频
				if nextCategoryId == startCategoryId {
					global.GVA_LOG.Error(fmt.Sprintf("%s, 父分类%d的所有子分类都没有视频", taskTitle, item.VideoCategoryId))
					video = nil
					break
				}

				currentCategoryId = nextCategoryId
			}

			// 如果所有子分类都没有视频，跳过这个任务
			if video == nil {
				continue
			}
		}

		// 确定记录中要使用的分类ID
		recordCategoryId := item.VideoCategoryId
		if len(subCategoryIds) > 0 {
			// 如果有子分类，使用实际找到视频的子分类ID
			recordCategoryId = int(actualCategoryId)
		}

		record := &creative.AutoPublishVideoRecord{
			DyUserId:        item.DyUserId,
			Weekday:         item.Weekday,
			Hour:            item.Hour,
			Minute:          item.Minute,
			Type:            item.Type,
			VideoCategoryId: recordCategoryId,
			SysUserId:       item.SysUserId,
			Nickname:        item.Nickname,
			UniqueId:        item.UniqueId,
			TimingType:      0,
		}
		err = videoLogic.UploadVideo(item.UniqueId, video, record)
		if err != nil {
			fmt.Printf("%s, 用户%s(%s):上传失败:%+v\n", taskTitle, item.Nickname, item.UniqueId, err)
			continue
		}
	}

	fmt.Printf("%s, 执行时间：%s:执行完毕\n", taskTitle, now.Format(time.DateTime))
	return nil
}

func (s AsyncUploadVideoTask) getSubCategoryIds(categoryId uint) []uint {
	var categoryIds []uint
	err := global.GVA_DB.Table("video_category").
		Where("parent_id = ?", categoryId).
		Where("status = 1").
		Order("created_at ASC").
		Pluck("id", &categoryIds).Error
	if err != nil {
		return []uint{}
	}
	return categoryIds
}

func (s AsyncUploadVideoTask) getCacheKey(pid uint) string {
	return fmt.Sprintf("publish_video_current_category_id:%d", pid)
}

func (s AsyncUploadVideoTask) setCurrentPublishCategoryId(pid, subId uint) {
	key := s.getCacheKey(pid)
	global.GVA_REDIS.Set(
		context.Background(),
		key,
		subId,
		0,
	)
}

func (s AsyncUploadVideoTask) getPublishCategoryId(pid uint, subIds []uint) uint {
	// 获取上一次发布的子分类
	key := s.getCacheKey(pid)
	subId, _ := global.GVA_REDIS.Get(context.Background(), key).Result()
	if subId == "" {
		return pid
	}
	// 根据上次发布子分类，获取下一个待发布子分类
	for i, id := range subIds {
		subIdUint, err := strconv.ParseUint(subId, 10, 64)
		if err != nil {
			global.GVA_LOG.Error("解析上一次发布的子分类ID失败", zap.Error(err))
			return pid
		}
		if id == uint(subIdUint) {
			if i == len(subIds)-1 {
				return pid
			}
			return subIds[i+1]
		}
	}
	return pid
}

func (s AsyncUploadVideoTask) getNextCategoryId(pid uint, subIds []uint, currentCategoryId uint) uint {
	for i, id := range subIds {
		if id == currentCategoryId {
			if i == len(subIds)-1 {
				return subIds[0]
			}
			return subIds[i+1]
		}
	}
	return pid
}
