package service

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/logic"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"go.uber.org/zap"
)

// 同步抖音作品
type SyncDyAwemeTask struct {
}

func (s SyncDyAwemeTask) Exec(arg any) (err error) {
	taskTitle := "同步抖音作品任务"

	// 获取dy_user中auto_publish_status=1的用户
	var dyUsers []*douyin.DyUser
	err = global.GVA_DB.Model(&douyin.DyUser{}).Where("auto_publish_status =?", 1).Find(&dyUsers).Error
	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s: 获取发布用户列表失败", taskTitle), zap.Error(err))
		return
	}

	fmt.Printf("%s: 开始执行, 共%d个用户", taskTitle, len(dyUsers))
	s.processDyUsers(dyUsers)

	global.GVA_LOG.Info(fmt.Sprintf("%s: 执行完成", taskTitle))
	return
}

/**
 * 定义协程池，防止并发过大
 */
func (s SyncDyAwemeTask) processDyUsers(dyUsers []*douyin.DyUser) {
	taskTitle := "同步抖音作品任务"
	dyAwemeLogic := logic.DyAwemeLogic{}
	// 定义协程池大小
	const workerCount = 30
	// 创建任务通道
	taskChan := make(chan *douyin.DyUser)

	var count int32 // 使用 int32 配合原子操作
	// 启动协程池
	var wg sync.WaitGroup
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for dyUser := range taskChan {
				atomic.AddInt32(&count, 1)

				err := s.processDyUser(dyUser, dyAwemeLogic)
				if err != nil {
					// 在redis中定义hash， memeber为uniqueId, 结构体包含处理时间，重试次数，当前重试次数为0
					data := struct {
						UniqueId   string
						Nickname   string
						RetryCount int
						Time       int64
						Reason     string
					}{
						UniqueId:   dyUser.UniqueId,
						RetryCount: 0,
						Time:       time.Now().Unix(),
						Nickname:   dyUser.Nickname,
						Reason:     err.Error(),
					}
					dataByte, _ := json.Marshal(data)
					global.GVA_REDIS.HSet(context.Background(), douyin.SyncDyAwemeRepairHashKey, dyUser.UniqueId, string(dataByte))
					global.GVA_LOG.Error(fmt.Sprintf("%s:处理用户%s失败!", taskTitle, dyUser.Nickname), zap.Error(err))
				}

				global.GVA_LOG.Info(fmt.Sprintf("%s:处理结束!", taskTitle), zap.String("nickname", dyUser.Nickname))
			}
		}()
	}

	// 分发任务
	for _, dyUser := range dyUsers {
		taskChan <- dyUser
	}

	// 关闭任务通道
	close(taskChan)

	// 等待所有协程完成
	wg.Wait()

	global.GVA_LOG.Info(fmt.Sprintf("%s: 处理结束，一共处理%d个", taskTitle, atomic.LoadInt32(&count)))
}

/**
 * 处理单个用户
 */
func (s SyncDyAwemeTask) processDyUser(dyUser *douyin.DyUser, dyAwemeLogic logic.DyAwemeLogic) (err error) {
	// 随机休眠0-5秒, 避免请求过于密集
	sleepTime := time.Duration(rand.Intn(5000)) * time.Millisecond
	time.Sleep(sleepTime)
	return dyAwemeLogic.ManageAwemeList(dyUser, 1)
}
