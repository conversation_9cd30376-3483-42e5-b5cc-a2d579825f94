package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/douyin/response"
	"go.uber.org/zap"
)

type DyAwemeLogic struct{}

// 管理作品列表
// disposeMethod 1-系统自动 2-人工处理
func (l *DyAwemeLogic) ManageAwemeList(dyUser *douyin.DyUser, disposeMethod int) error {
	var err error
	cookieStr, err := dyUserService.GetAwemeCookie(dyUser.UniqueId)
	if err != nil || cookieStr == "" {
		err = fmt.Errorf("未找到有效的cookie")
		return err
	}

	awemes := make([]response.MoreCreatorAdvanceApiAwemeInfo, 0)
	maxCount := 12
	var maxCursor int64 = 0
	i := 1
	ok := true
	for ok {
		count := maxCount
		req := request.MoreCreatorAdvanceApiWorkListRequest{
			Cookie: cookieStr,
			Count:  count,
			Status: 0,
			Proxy:  dyUser.BindIP,
		}
		global.GVA_LOG.Info(fmt.Sprintf("用户:%s,第%d次获取作品列表, cursor:%d, count:%d \n", dyUser.Nickname, i, maxCursor, count))

		if maxCursor != 0 {
			req.MaxCursor = fmt.Sprintf("%d", maxCursor)
		}
		resp, respErr := moreCreatorAdvanceApiService.GetWorkList(req)
		if respErr != nil {
			err = fmt.Errorf("【%s】 %s", dyUser.Nickname, respErr.Error())
			break
		}
		if resp.Code != 200 {
			err = fmt.Errorf("【%s】获取作品列表失败：code: %d,message:%s, msg:%s", dyUser.Nickname, resp.Code, resp.Message, resp.Msg)
			break
		}
		if resp.Data.StatusCode != 0 {
			if resp.Data.StatusCode == 8 {
				// 账号被封禁
				err = fmt.Errorf("【%s】账号已掉线，请重新登录,status_code:%d", dyUser.Nickname, resp.Data.StatusCode)
				break
			}
			err = fmt.Errorf("【%s】获取作品列表失败：code: %d,message:%s", dyUser.Nickname, resp.Data.StatusCode, resp.Data.StatusMessage)
			break
		}

		awemes = resp.Data.AwemeList
		if len(awemes) == 0 {
			fmt.Printf("用户:%s,第%d次获取作品列表,作品为空。 cursor:%d, count:%d \n", dyUser.Nickname, i, maxCursor, count)
			ok = false
			break
		}

		for _, aweme := range awemes {
			// 通过aweme的awmeId从数据库dyAweme获取记录，如果不存在则插入
			isNew := false
			awemeInfo, awemeErr := dyAwemeService.GetAwemeInfoByAwemeId(aweme.AwemeId)
			if awemeErr != nil {
				if awemeErr.Error() != "record not found" {
					continue
				}

				isNew = true
				awemeInfo = douyin.DyAweme{
					AwemeId:        aweme.AwemeId,
					AwemeType:      aweme.AwemeType,
					CreateTime:     aweme.CreateTime,
					SysDispose:     0,
					SysDisposeTime: 0,
					SysUserId:      dyUser.SysUserId,
				}
			}

			cover := ""
			if len(aweme.Cover.UrlList) > 0 {
				cover = aweme.Cover.UrlList[0]
			}
			awemeInfo.Cover = cover
			awemeInfo.Nickname = aweme.Author.Nickname
			awemeInfo.UniqueId = aweme.Author.UniqueId
			awemeInfo.Desc = aweme.Desc
			awemeInfo.Duration = aweme.Duration
			awemeInfo.GroupId = fmt.Sprintf("%v", aweme.GroupId)
			awemeInfo.SysDisposeMethod = disposeMethod

			// 作品状态信息
			awemeInfo.StatusValue = aweme.StatusValue
			statusByte, _ := json.Marshal(aweme.Status)
			awemeInfo.Status = string(statusByte)
			awemeInfo.PrivateStatus = aweme.Status.PrivateStatus
			awemeInfo.InReviewing = 0
			if aweme.Status.InReviewing {
				awemeInfo.InReviewing = 1
			}

			// 作品统计信息
			if awemeInfo.PrivateStatus != 1 {
				StatisticsByte, _ := json.Marshal(aweme.Statistics)
				awemeInfo.Statistics = string(StatisticsByte)
				awemeInfo.CommentCount = aweme.Statistics.CommentCount
				awemeInfo.DiggCount = aweme.Statistics.DiggCount
				awemeInfo.ShareCount = aweme.Statistics.ShareCount
				awemeInfo.PlayCount = aweme.Statistics.PlayCount

				awemeInfo.ReviewStatus = aweme.ReviewStruct.Status
				// 作品审核信息
				newReviewDesc := aweme.ReviewStruct.StatusDesc
				if newReviewDesc != "" {
					// 如果流量减少，aweme.ReviewStruct.StatusDesc值为”流量减少“，则修改权限为私密
					if newReviewDesc == "流量减少" || newReviewDesc == "限制互关可见" {
						awemeInfo.SysDispose = 10
					} else if newReviewDesc == "限制自己可见" {
						// ”限制自己可见“，则删除作品
						awemeInfo.SysDispose = 12
					}

					reviewStructByte, _ := json.Marshal(aweme.ReviewStruct)
					awemeInfo.ReviewStruct = string(reviewStructByte)
				}

				// “不适宜公开”，则删除作品
				if aweme.Status.SelfSee && newReviewDesc == "" {
					newReviewDesc = "不适宜公开"
					awemeInfo.SysDispose = 12
				}

				if newReviewDesc == "" {
					now := time.Now().Unix()
					if now-awemeInfo.CreateTime > 21600 && awemeInfo.PlayCount == 0 {
						newReviewDesc = "超过6小时播放量仍为0"
						awemeInfo.SysDispose = 10
					}
				}

				if newReviewDesc != "" {
					if awemeInfo.ReviewStatusDesc != "" {
						descArr := strings.Split(awemeInfo.ReviewStatusDesc, ",")
						if descArr[len(descArr)-1] != newReviewDesc {
							awemeInfo.ReviewStatusDesc = fmt.Sprintf("%s,%s", awemeInfo.ReviewStatusDesc, newReviewDesc)
						}
					} else {
						awemeInfo.ReviewStatusDesc = newReviewDesc
					}
				}
			} else {
				if awemeInfo.SysDispose == 9 || awemeInfo.SysDispose == 10 || awemeInfo.SysDispose == 11 || awemeInfo.SysDispose == 12 {
					awemeInfo.SysDispose = 2
				}
			}

			if isNew {
				err = dyAwemeService.CreateAweme(&awemeInfo)
			} else {
				err = dyAwemeService.UpdateAweme(&awemeInfo)
			}
		}
		if maxCursor == resp.Data.MaxCursor {
			// 最后一页
			global.GVA_LOG.Info(fmt.Sprintf("用户:%s, 最后一页,次数:%d, cursor:%d, count:%d \n", dyUser.Nickname, i, maxCursor, count))
			ok = false
			break
		}
		global.GVA_LOG.Info(fmt.Sprintf("用户:%s,第%d次获取作品列表完成, count:%d, reqMaxCursor:%s, respCursor:%d \n", dyUser.Nickname, i, count, req.MaxCursor, resp.Data.MaxCursor))
		err = global.GVA_REDIS.SAdd(context.Background(), douyin.DisposeAwemeUserSetKey, dyUser.UniqueId).Err()
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("用户%s: 添加到作品待处理集合失败", dyUser.Nickname), zap.Error(err))
		}
		maxCursor = resp.Data.MaxCursor
		time.Sleep(15 * time.Second)

		i++
		global.GVA_LOG.Info(fmt.Sprintf("用户:%s,准备获取第%d次获取作品列表, cursor:%d, count:%d \n", dyUser.Nickname, i, maxCursor, count))
	}
	if err != nil {
		return err
	}

	return nil
}

// 修改作品权限
// disposeMethod 1-系统自动,2-人工处理
func (l *DyAwemeLogic) EditAwemePermission(dyUser *douyin.DyUser, aweme *douyin.DyAweme, privateStatus int, disposeMethod int) (err error) {
	// if aweme.PrivateStatus != PrivateStatus {
	cookieStr, err := dyUserService.GetAwemeCookie(dyUser.UniqueId)
	if err != nil || cookieStr == "" {
		err = fmt.Errorf("未找到有效的cookie:%s", dyUser.UniqueId)
		return err
	}
	req := request.MoreCreatorAdvanceApiWorkModifyRequest{
		Cookie:         cookieStr,
		ItemId:         aweme.AwemeId,
		VisibilityType: privateStatus,
		Proxy:          dyUser.BindIP,
	}
	resp, err := moreCreatorAdvanceApiService.UpdateWorkVisibility(req)
	if err != nil {
		return err
	}

	aweme.SysDisposeMethod = disposeMethod
	aweme.SysDisposeTime = time.Now().Unix()

	// 作品被删除
	if resp.Data.StatusCode == 533 {
		fmt.Printf("作品被删除：%s, aweme_id:%s \n", aweme.Desc, aweme.AwemeId)
		aweme.SysDispose = 4
		return
	}

	if resp.Data.StatusCode != 533 && resp.Data.StatusCode != 0 {
		return fmt.Errorf("修改抖音作品失败：status_code: %d,status_msg:%s", resp.Data.StatusCode, resp.Data.StatusMsg)
	}

	aweme.PrivateStatus = privateStatus
	if disposeMethod == 1 {
		aweme.SysDispose = privateStatus + 1
	} else {
		aweme.SysDispose = privateStatus + 5
	}

	return
}

// 删除作品
// disposeMethod: 1-系统自动,2-人工处理
func (l *DyAwemeLogic) DeleteAweme(dyUser *douyin.DyUser, aweme *douyin.DyAweme, disposeMethod int) (err error) {
	cookieStr, err := dyUserService.GetAwemeCookie(dyUser.UniqueId)
	req := request.MoreCreatorAdvanceApiWorkDeleteRequest{
		Cookie: cookieStr,
		ItemId: aweme.AwemeId,
		Proxy:  dyUser.BindIP,
	}
	_, err = moreCreatorAdvanceApiService.DeleteWork(req)
	if err != nil {
		return err
	}
	if disposeMethod == 1 {
		aweme.SysDispose = 4
	} else {
		aweme.SysDispose = 8
	}
	aweme.PrivateStatus = 4

	aweme.SysDisposeMethod = disposeMethod
	aweme.SysDisposeTime = time.Now().Unix()

	return
}
