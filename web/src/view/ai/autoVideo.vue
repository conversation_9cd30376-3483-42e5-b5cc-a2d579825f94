<template>
  <div class="auto-video-container">
    <el-card class="auto-video-card">
      <div class="page-layout">
        <div class="main-config-area">
          <el-form :model="formData" label-position="right" label-width="120px" class="auto-video-form">
            <TopSettings
              :settings="formData.topSettings"
              @update:settings="formData.topSettings = $event"
              :transitionSettings="formData.transitionSettings"
              @update:transitionSettings="updateTransitionSettings"
              :filterSettings="formData.filterSettings"
              @update:filterSettings="formData.filterSettings = $event"
              :effectSettings="formData.effectSettings"
              @update:effectSettings="formData.effectSettings = $event"
              :effectRatioSettings="formData.outputSettings"
              @update:effectRatioSettings="formData.outputSettings = $event"
              :stickers="formData.stickers"
              @update:stickers="formData.stickers = $event"
            />

            <!-- 主要配置区域，使用 Tabs -->
            <div class="main-content">
              <el-tabs v-model="activeTab" class="config-tabs">
                <!-- 镜头素材 Tab -->
                <el-tab-pane name="lens">
                  <template #label>
                    <span>
                      <el-icon>
                        <Camera />
                      </el-icon>
                      <span style="margin-left: 5px">镜头素材</span>
                    </span>
                  </template>
                  <LensMaterials
                    :materials="formData.lensMaterials"
                    :globalVoice="formData.topSettings.globalVoice"
                    @update:materials="formData.lensMaterials = $event"
                    ref="lensMaterialsRef"
                  />
                </el-tab-pane>

                <!-- 标题设置 Tab -->
                <el-tab-pane name="title">
                  <template #label>
                    <span>
                      <el-icon>
                        <Document />
                      </el-icon>
                      <span style="margin-left: 5px">标题设置</span>
                    </span>
                  </template>
                  <div class="title-settings-wrapper">
                    <TitleSettings
                      :titles="formData.titles"
                      @update:titles="handleTitlesUpdate"
                      :aspectRatio="formData.outputSettings.aspectRatio"
                      :materials="formData.lensMaterials"
                      :stickers="formData.stickers"
                      :videoOutputWidth="formData.outputSettings.videoOutputWidth"
                      :videoOutputHeight="formData.outputSettings.videoOutputHeight"
                      @active-title-changed="handleActiveTitleChanged"
                    />
                  </div>
                </el-tab-pane>

                <!-- 背景音乐 Tab -->
                <el-tab-pane name="music">
                  <template #label>
                    <span>
                      <el-icon>
                        <Headset />
                      </el-icon>
                      <span style="margin-left: 5px">背景音乐</span>
                    </span>
                  </template>
                  <BackgroundMusic
                    :musics="formData.backgroundMusics"
                    @update:musics="formData.backgroundMusics = $event"
                  />
                </el-tab-pane>

                <!-- 其他设置 Tab -->
                <el-tab-pane name="other">
                  <template #label>
                    <span>
                      <el-icon>
                        <Setting />
                      </el-icon>
                      <span style="margin-left: 5px">其他设置</span>
                    </span>
                  </template>
                  <OtherSettings
                    :settings="formData.outputSettings"
                    :selectedCategories="selectedCategories"
                    :categoryOptions="categoryOptions"
                    :topicSettings="formData.topicSettings"
                    :topicCategoryOptions="topicCategoryOptions"
                    :topicOptions="topicOptions"
                    :manualTopicToAdd="manualTopicToAdd"
                    @update:settings="updateGenerateCount"
                    @update:selectedCategories="selectedCategories = $event"
                    @update:topicSettings="formData.topicSettings = $event"
                    @update:topicOptions="topicOptions = $event"
                    @update:manualTopicToAdd="manualTopicToAdd = $event"
                  />
                </el-tab-pane>
              </el-tabs>

              <!-- 底部提交按钮 -->
              <div class="form-actions">
                <el-button type="primary" @click="handleSubmit" :loading="loading" size="large" class="submit-btn">
                  生成任务
                </el-button>
              </div>
            </div>
          </el-form>
        </div>
        <div class="preview-area-container">
          <VideoPreview
            :titles="formData.titles"
            :stickers="formData.stickers"
            @update:stickers="formData.stickers = $event"
            :aspectRatio="formData.outputSettings.aspectRatio"
            :materials="formData.lensMaterials"
            :videoOutputWidth="formData.outputSettings.videoOutputWidth"
            :videoOutputHeight="formData.outputSettings.videoOutputHeight"
            :activeTitleIndex="currentActiveTitleIndex"
            :clipSettings="formData.topSettings"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
  export default {
    name: 'AutoVideo'
  }
</script>

<script setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Camera, Document, Headset, Setting } from '@element-plus/icons-vue'
  import { submitAutoVideoTask } from '@/api/ai/auto-video'
  import { getVideoCategoryList } from '@/api/creative/videoCategory'
  import { getCategoryList } from '@/api/media/topicCategory'
  import VideoPreview from '@/components/autoVideo/VideoPreview.vue'

  // 导入组件
  import TopSettings from '@/components/autoVideo/TopSettings.vue'
  import LensMaterials from '@/components/autoVideo/LensMaterials.vue'
  import TitleSettings from '@/components/autoVideo/TitleSettings.vue'
  import BackgroundMusic from '@/components/autoVideo/BackgroundMusic.vue'
  import OtherSettings from '@/components/autoVideo/OtherSettings.vue'

  // 创建子组件引用
  const lensMaterialsRef = ref(null)

  // 添加 activeTab 状态
  const activeTab = ref('lens')

  // 加载状态
  const loading = ref(false)
  const selectedCategories = ref([])
  const categoryOptions = ref([])
  const topicCategoryOptions = ref([])
  const topicOptions = ref([])
  const manualTopicToAdd = ref(null)

  // 表单数据
  const formData = reactive({
    // 顶部设置
    topSettings: {
      videoVolume: 0, // 视频原生音量: 0
      voiceVolume: 1.8, // 配音音量: 1.8
      backgroundVolume: 0.2, // 背景音乐音量: 0.2
      voiceSpeed: 1, // 语音配速: 1
      showSubtitle: true, // 显示字幕
      globalVoice: false, // 全局配音
      disableRandomSubtitle: false, // 禁用随机字幕
      subtitleStyle: {
        // 字幕样式
        height: 0.75, // 文字高度(默认75%)
        fontFamily: 'Alibaba PuHuiTi', // 默认字体改为 阿里巴巴普惠体
        fontSize: 60, // 字号 (默认60)
        alignment: 'Center', // 对齐方式
        fontStyle: {
          type: 'flower', // 默认样式类型改为 flowerel-radio
          color: '#ffffff', // 字体颜色
          styleType: 'none', // 默认无额外样式
          backgroundColor: '', // 背景颜色
          borderColor: '', // 边框颜色
          borderWidth: 1 // 边框宽度
        },
        flowerStyle: '', // 花字样式
        randomEffect: true, // 默认开启随机效果
        imageDuration: 2 // 默认图片素材时长为2秒
      },
      globalSubtitles: [''], // 全局字幕列表，只存储内容字符串
      actorVoice: 'zhimiao_emo' // 配音演员
    },

    // 镜头素材
    lensMaterials: [
      {
        mediaId: '', // 媒体ID
        mediaUrl: '', // 媒体URL
        type: 'video', // 类型：video/image
        narration: '', // 口播内容（单个，兼容旧版本）
        narrations: undefined, // 口播内容（多个）
        extraMedia: [] // 额外素材
      }
    ],

    // 标题列表
    titles: [],

    // 背景音乐
    backgroundMusics: [],

    // 贴纸列表
    stickers: [],

    // 输出设置
    outputSettings: {
      aspectRatio: '9:16', // 视频比例：9:16
      outputMode: 'speech', // 输出模式：视频时长/配音时长
      generateCount: undefined, // 生成数量 (修改默认值为 undefined)
      videoTitle: '', // 视频标题
      videoOutputWidth: 1080, // Default output width for 9:16
      videoOutputHeight: 1920 // Default output height for 9:16
    },

    // 转场设置
    transitionSettings: {
      randomTransition: false, // 随机转场
      transitionList: [] // 转场列表
    },

    // 滤镜设置
    filterSettings: {
      randomFilter: false, // 随机滤镜
      filterList: [], // 滤镜列表
      contrast: 0, // 对比度
      saturation: 0, // 饱和度
      brightness: 0, // 亮度
      hue: 0 // 色度
    },

    // 添加特效设置
    effectSettings: {
      randEffect: false, // 随机特效
      effectProbability: 1, // 特效应用概率
      effectList: [], // 选中特效列表
      type: 'normal' // 特效类型 normal/advanced
    },

    topicSettings: {
      categoryId: null, // 选中的话题分类ID
      selectionMode: 'random', // 'none': 不使用, 'manual': 手动选择, 'random': 随机选择
      coreTopics: [], // Array of { name: string, id: number|null, source: 'random' | 'manual' }
      finalTopicGroups: [], // Array of string arrays (final topics for each core topic)
      finalTopicOptions: [], // Array of arrays (API results for each core topic dropdown)
      customTopicGroups: [], // Array of string arrays (custom topics for each core topic)
      searchStatus: [] // Array of 'idle' | 'loading' | 'success' | 'error'
    }
  })

  // 更新转场设置
  const updateTransitionSettings = (settings) => {
    formData.transitionSettings = settings
  }

  // 更新生成数量
  const updateGenerateCount = (settings) => {
    formData.outputSettings = {
      ...formData.outputSettings,
      ...settings
    }
  }

  // 在组件加载时获取分类数据
  onMounted(async () => {
    try {
      await loadCategories() // 获取视频分类
      await loadTopicCategories() // 获取话题分类
    } catch (error) {
      console.error('Error during onMounted execution:', error)
      ElMessage.error('页面加载期间发生错误，部分数据可能无法加载')
    }
  })

  // 加载视频分类
  const loadCategories = async () => {
    try {
      // 使用创意分类API获取树形结构的视频分类
      const response = await getVideoCategoryList()
      if (response.code === 0 && response.data && response.data.list) {
        // 将创意分类数据转换为级联选择器需要的格式
        categoryOptions.value = response.data.list.map((item) => ({
          value: item.ID,
          label: item.name,
          children:
            item.children && item.children.length > 0
              ? item.children.map((child) => ({
                  value: child.ID,
                  label: child.name,
                  children:
                    child.children && child.children.length > 0
                      ? child.children.map((grandChild) => ({
                          value: grandChild.ID,
                          label: grandChild.name
                        }))
                      : undefined
                }))
              : undefined
        }))
      } else {
        categoryOptions.value = []
        ElMessage.warning('获取视频分类失败: ' + (response.message || '数据格式错误或无数据'))
      }
    } catch (error) {
      console.error('获取视频分类失败:', error)
      categoryOptions.value = []
      ElMessage.warning('获取视频分类失败')
    }
  }

  // 加载话题分类
  const loadTopicCategories = async () => {
    try {
      const response = await getCategoryList()
      // 检查 response.data 是否直接是数组
      if (response.code === 0 && Array.isArray(response.data)) {
        // 直接使用树形结构
        topicCategoryOptions.value = response.data
      } else {
        // 如果结构不符合预期或code不为0，则记录错误
        console.error('Unexpected API response structure or error code:', response)
        topicCategoryOptions.value = [] // 重置选项
        ElMessage.warning('获取话题分类失败: ' + (response.msg || '数据格式错误或无数据'))
      }
    } catch (error) {
      console.error('获取话题分类失败:', error)
      topicCategoryOptions.value = [] // 捕获错误时重置选项
      ElMessage.warning('获取话题分类失败')
    }
  }

  // 修改handleSubmit方法，移除弹窗显示
  const handleSubmit = async () => {
    try {
      // 在验证表单前，先调用镜头组件的验证方法，过滤无效镜头
      if (lensMaterialsRef.value) {
        formData.lensMaterials = lensMaterialsRef.value.validateMaterials()

        // 验证镜头时长设置
        const durationValidation = lensMaterialsRef.value.validateLensDurations()
        if (!durationValidation.valid) {
          ElMessage.warning(durationValidation.message)
          activeTab.value = 'lens' // 切换到镜头素材 Tab
          return
        }
      }

      if (!validateForm()) {
        return
      }

      loading.value = true

      // 构建提交参数
      const params = buildSubmitParams()
      // 加入分类数据
      params.categoryIds = selectedCategories.value
      // 发送请求
      const response = await submitAutoVideoTask(params)

      // 判断返回值类型处理
      if (typeof response === 'string') {
        // 直接返回的是任务ID字符串
        ElMessage.success('任务提交成功，正在后台生成视频，请耐心等待...')
      } else if (response && response.code === 0) {
        // 标准响应格式
        ElMessage.success('任务提交成功，正在后台生成视频，请耐心等待...')
      } else {
        ElMessage.error((response && response.message) || '提交任务失败')
        loading.value = false
        return
      }

      // 任务提交成功后将生成数量置空
      formData.outputSettings.generateCount = undefined

      // 任务提交成功后重置表单或跳转页面（可选）
      // resetForm(); // 如果需要重置表单
    } catch (error) {
      console.error('提交任务出错:', error)
      ElMessage.error('提交任务出错: ' + (error.message || '未知错误'))
    } finally {
      // 保持按钮loading状态5秒后才恢复
      setTimeout(() => {
        loading.value = false
      }, 5000)
    }
  }

  // 验证表单
  const validateForm = () => {
    // 过滤有效的镜头素材（mediaId或mediaUrl不为空）
    const validMaterials = formData.lensMaterials.filter((m) => m.mediaId || m.mediaUrl)

    // 如果有有效的镜头素材，更新formData中的镜头素材
    if (validMaterials.length > 0) {
      formData.lensMaterials = validMaterials
    }

    // 检查是否至少有一个有效的镜头素材
    if (validMaterials.length === 0) {
      ElMessage.warning('请至少添加一个镜头素材')
      activeTab.value = 'lens' // 切换到镜头素材 Tab
      return false
    }

    // 检查生成数量是否已填写且大于0
    if (
      formData.outputSettings.generateCount === null ||
      formData.outputSettings.generateCount === undefined ||
      formData.outputSettings.generateCount <= 0
    ) {
      ElMessage.warning('请填写有效的生成数量 (必须大于0)')
      activeTab.value = 'other' // 切换到其他设置 Tab
      return false
    }

    // 检查是否选择了视频分类
    if (selectedCategories.value.length === 0) {
      ElMessage.warning('请选择视频分类')
      activeTab.value = 'other' // 切换到其他设置 Tab
      return false
    }

    // 如果开启了全局配音，检查是否有全局字幕内容
    if (formData.topSettings.globalVoice) {
      const hasValidSubtitle = formData.topSettings.globalSubtitles.some((s) => s.trim() !== '')
      if (!hasValidSubtitle) {
        ElMessage.warning('开启全局配音后，请至少添加一条全局字幕内容')
        return false
      }
    }

    // 检查标题和话题设置，如果都没有则视频标题必填
    const hasTitles =
      formData.titles &&
      formData.titles.length > 0 &&
      formData.titles.some(
        (title) =>
          (title.content && title.content.trim()) ||
          (title.contents && title.contents.length > 0 && title.contents.some((c) => c.trim()))
      )

    const hasTopics =
      formData.topicSettings.selectionMode !== 'none' &&
      formData.topicSettings.finalTopicGroups &&
      formData.topicSettings.finalTopicGroups.length > 0 &&
      formData.topicSettings.finalTopicGroups.some((group) => group.length > 0)

    if (!hasTitles && !hasTopics) {
      const videoTitle = formData.outputSettings.videoTitle?.trim()
      if (!videoTitle) {
        ElMessage.warning('当标题设置中没有标题且话题设置为空时，视频标题为必填项')
        activeTab.value = 'other' // 切换到其他设置 Tab
        return false
      }
    }

    return true
  }

  // 构建提交参数 (Uses unified state)
  const buildSubmitParams = () => {
    // 使用标题列表中的第一个标题作为任务名称
    const mainTitleText =
      formData.titles && formData.titles.length > 0 && formData.titles[0].content
        ? formData.titles[0].content.trim()
        : ''

    let finalTopicGroupsToSend = []
    if (formData.topicSettings.selectionMode === 'random' || formData.topicSettings.selectionMode === 'manual') {
      finalTopicGroupsToSend = formData.topicSettings.finalTopicGroups
    }

    // 检查是否有镜头使用了音乐时长
    const hasMusicDurationLens = formData.lensMaterials.some((lens) => lens.useMusicDuration)

    // 获取背景音乐时长数组
    const backgroundMusicDurations = formData.backgroundMusics.map((item) => item.duration || 0)

    const params = {
      taskName: mainTitleText ? mainTitleText : '一键成片-' + new Date().toLocaleString(),
      videoVolume: formData.topSettings.videoVolume,
      voiceVolume: formData.topSettings.voiceVolume,
      backgroundVolume: formData.topSettings.backgroundVolume,
      voiceSpeed: formData.topSettings.voiceSpeed,
      contrast: formData.filterSettings?.contrast || 0,
      saturation: formData.filterSettings?.saturation || 0,
      brightness: formData.filterSettings?.brightness || 0,
      hue: formData.filterSettings?.hue || 0,
      randomTransition: formData.transitionSettings.randomTransition,
      randomFilter: formData.filterSettings?.randomFilter || false,
      randEffect: formData.effectSettings?.randEffect || false,
      effectProbability: formData.effectSettings?.effectProbability || 0,
      effectList: (formData.effectSettings?.effectList || []).map((item) => item.subType),
      effectType: formData.effectSettings?.type || 'normal',
      showSubtitle: formData.topSettings.showSubtitle,
      globalVoice: formData.topSettings.globalVoice,
      globalSubtitleList: formData.topSettings.globalSubtitles,
      subtitleStyle: formData.topSettings.subtitleStyle,
      actorVoice: formData.topSettings.actorVoice,
      lensMaterials: formData.lensMaterials,
      titleList: formData.titles || [],
      backgroundMusics: formData.backgroundMusics.map((item) => item.mediaId || ''),
      backgroundMusicDurations: backgroundMusicDurations, // 添加背景音乐时长数组
      stickers: formData.stickers,
      aspectRatio: formData.outputSettings?.aspectRatio || '16:9',
      outputMode: formData.outputSettings?.outputMode || 'speech',
      generateCount: formData.outputSettings?.generateCount || 1,
      transitionList: formData.transitionSettings.transitionList,
      filterList: formData.filterSettings?.filterList || [],
      topics: finalTopicGroupsToSend,
      categoryIds: selectedCategories.value,
      videoTitle: formData.outputSettings.videoTitle || '', // 添加视频标题
      useMusicDuration: hasMusicDurationLens, // 添加顶层useMusicDuration参数
      disableRandomSubtitle: formData.topSettings.disableRandomSubtitle, // 添加disableRandomSubtitle参数
      manualProductId: formData.outputSettings.manualProductId || 0 // 添加手动录入商品ID
    }

    return params
  }

  const currentActiveTitleIndex = ref(-1)

  const handleActiveTitleChanged = (newIndex) => {
    currentActiveTitleIndex.value = newIndex
  }

  // 新添加的方法，用于安全处理标题更新
  const handleTitlesUpdate = (newTitles) => {
    if (!Array.isArray(newTitles)) {
      console.error('接收到无效的标题数据')
      return
    }

    // 使用验证方法确保数据完整
    formData.titles = validateTitles(newTitles)
  }

  // 监听视频比例变化，同步更新 formData.outputSettings 中的 videoOutputWidth 和 videoOutputHeight
  watch(
    () => formData.outputSettings.aspectRatio,
    (newRatio) => {
      if (newRatio === '9:16') {
        formData.outputSettings.videoOutputWidth = 1080
        formData.outputSettings.videoOutputHeight = 1920
      } else if (newRatio === '16:9') {
        formData.outputSettings.videoOutputWidth = 1920
        formData.outputSettings.videoOutputHeight = 1080
      } else if (newRatio === '1:1') {
        formData.outputSettings.videoOutputWidth = 1080
        formData.outputSettings.videoOutputHeight = 1080
      } else {
        formData.outputSettings.videoOutputWidth = 1080
        formData.outputSettings.videoOutputHeight = 1920
      }
    },
    { immediate: true }
  )

  // 在任何formData.titles可能发生变化的地方，添加数据验证
  const validateTitles = (titles) => {
    if (!Array.isArray(titles)) return []

    return titles.map((title) => {
      // 验证每个标题项
      const validatedTitle = { ...title }

      // 确保基本属性
      if (!validatedTitle.id) {
        validatedTitle.id = Date.now() + Math.random().toString(36).substring(2, 9)
      }

      // 处理副标题
      if (validatedTitle.type === 'sub') {
        // 确保有contents数组
        if (!Array.isArray(validatedTitle.contents)) {
          validatedTitle.contents = validatedTitle.content ? [validatedTitle.content] : ['']
        }
        // 确保有content属性（兼容性）
        validatedTitle.content = validatedTitle.contents[0] || ''
      }
      // 处理主标题
      else if (validatedTitle.type === 'main') {
        if (validatedTitle.content === undefined) {
          validatedTitle.content = ''
        }
      }

      return validatedTitle
    })
  }
</script>

<style scoped>
  .auto-video-container {
    max-width: 1600px;
    margin: 0 auto;
  }

  .auto-video-card {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .card-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    color: white;
  }

  .card-title {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
    margin-bottom: 5px;
  }

  .card-description {
    font-size: 14px;
    opacity: 0.9;
  }

  .main-content {
    margin-top: 20px;
  }

  .form-actions {
    margin-top: 30px;
    text-align: center;
    padding: 10px 0;
  }

  .submit-btn {
    padding: 12px 36px;
    font-size: 16px;
    background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    border: none;
    transition: all 0.3s;
  }

  .submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(75, 108, 183, 0.4);
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .auto-video-form {
      padding: 15px;
    }
  }

  /* 添加新样式 */
  .top-settings-container {
    width: 100%;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);
    border-radius: 4px;
    padding: 10px 20px;
  }

  .form-item-container {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 6px;
    background-color: #fdfdfd;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    border-left: 4px solid #4b6cb7;
    padding-left: 10px;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .config-tabs .el-tab-pane {
    padding: 15px 0;
  }

  .page-layout {
    display: flex;
    gap: 20px;
    /* 左右两栏的间距 */
  }

  .main-config-area {
    flex: 3;
    /* 左侧配置区域占比，可根据需要调整 */
    min-width: 0;
    /* 防止内容溢出导致flex布局问题 */
    display: flex;
    /* 使内部的el-form可以正确伸展 */
    flex-direction: column;
  }

  .auto-video-form {
    /* 确保表单填满其在main-config-area中的空间 */
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .main-content {
    /* 确保主要内容区域 (tabs 和 actions) 填满表单中的剩余空间 */
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .config-tabs {
    /* 确保tabs组件填满其在main-content中的空间 */
    flex-grow: 1;
  }

  .preview-area-container {
    flex: 2;
    /* 右侧预览区域占比，可根据需要调整 */
    min-width: 0;
    max-width: 30%;
    /* 防止内容溢出导致flex布局问题 */
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    position: sticky;
    /* 粘性定位 */
    top: 20px;
    /* 距离视口顶部的距离 */
    align-self: flex-start;
    /* 确保sticky在flex容器中正确工作 */
    max-height: calc(100vh - 90px);
    /* 最大高度，超出则滚动 (90px为顶部导航和间距预留) */
    overflow-y: auto;
    /* 内容超出时显示滚动条 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .preview-title {
    font-size: 1.1rem;
    /* 17.6px */
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }

  .preview-content-placeholder {
    border: 2px dashed #d9d9d9;
    padding: 20px;
    min-height: 250px;
    /* 预览区域的最小高度 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #757575;
    border-radius: 4px;
    font-size: 0.9rem;
    line-height: 1.6;
  }

  .preview-content-placeholder p {
    margin-bottom: 8px;
  }

  .preview-content-placeholder p:last-of-type {
    margin-bottom: 0;
  }

  .preview-content-placeholder code {
    background-color: #f0f0f0;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
    color: #555;
  }
</style>
