<template>
  <div class="other-settings">
    <!-- 生成数量和视频分类设置 -->
    <div class="form-item-container">
      <h3 class="section-title">基础设置</h3>
      <div class="section-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生成数量" label-width="80px">
              <el-input-number
                v-model="localSettings.generateCount"
                :min="1"
                :max="100"
                :step="1"
                controls-position="right"
                @change="handleGenerateCountChange"
                style="width: 100%"
              />
              <div class="el-upload__tip" style="line-height: 1.4; margin-top: 5px">最多生成100个视频</div>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="视频分类" label-width="80px">
              <el-select v-model="localSelectedCategories" multiple placeholder="请选择视频分类" style="width: 100%">
                <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
              <div class="el-upload__tip" style="line-height: 1.4; margin-top: 5px">
                选择视频分类，我们会按照所选分类循环分配给生成的视频
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="视频标题" label-width="80px">
              <el-input v-model="localSettings.videoTitle" placeholder="请输入视频标题" clearable style="width: 100%" />
              <div class="el-upload__tip" style="line-height: 1.4; margin-top: 5px">
                当标题设置中没有标题且话题设置为空时，此项必填。此标题将作为AI生成标题的依据
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 话题选择 -->
    <div class="form-item-container">
      <h3 class="section-title">
        话题选择 (可选)
        <el-button
          v-if="localTopicSettings.coreTopics.length > 0"
          type="text"
          @click="toggleTopicExpanded"
          style="margin-left: 10px; font-size: 14px"
        >
          {{ isTopicExpanded ? '收起' : '展开' }}
          <el-icon style="margin-left: 4px">
            <ArrowDown v-if="!isTopicExpanded" />
            <ArrowUp v-if="isTopicExpanded" />
          </el-icon>
        </el-button>
      </h3>
      <el-alert
        title="选择话题分类后，可手动选择或随机选择话题，我们会从该分类下随机选择话题添加到视频中"
        type="warning"
        show-icon
        :closable="false"
        style="margin-bottom: 15px"
      />
      <div class="section-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="话题分类" label-width="80px">
              <el-cascader
                v-model="localTopicSettings.categoryId"
                :options="topicCategoryOptions"
                :props="{
                  checkStrictly: true,
                  emitPath: false,
                  value: 'ID',
                  label: 'name',
                  children: 'children',
                  expandTrigger: 'click'
                }"
                placeholder="选择话题分类"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="选择模式" label-width="80px">
              <el-radio-group v-model="localTopicSettings.selectionMode" :disabled="!localTopicSettings.categoryId">
                <el-radio value="random">随机选择</el-radio>
                <el-radio value="manual">手动选择</el-radio>
                <el-radio value="none">不使用话题</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 话题内容区域 - 可收缩 -->
        <div v-show="isTopicExpanded">
          <template v-if="localTopicSettings.selectionMode === 'manual'">
            <el-row :gutter="20" style="margin-bottom: 15px; align-items: center">
              <el-col :span="16">
                <el-form-item label="添加核心话题" label-width="100px">
                  <el-select
                    v-model="localManualTopicToAdd"
                    placeholder="从分类中选择话题"
                    clearable
                    filterable
                    style="width: 100%"
                    :disabled="
                      !localTopicSettings.categoryId ||
                      topicOptions.length === 0 ||
                      localTopicSettings.coreTopics.length >= (settings.generateCount || 1)
                    "
                  >
                    <el-option v-for="item in topicOptions" :key="item.id" :label="item.name" :value="item.id" />
                    <template #empty>
                      <div style="text-align: center; color: #999; padding: 10px 0">
                        {{ localTopicSettings.categoryId ? '此分类下无话题' : '请先选择话题分类' }}
                      </div>
                    </template>
                  </el-select>
                  <div class="el-upload__tip" style="line-height: 1.4; margin-top: 5px">
                    选择一个话题作为视频的核心主题（最多
                    {{ settings.generateCount || 1 }} 个）。
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8" style="padding-top: 5px">
                <el-button type="primary" @click="addManualCoreTopic" :disabled="!localManualTopicToAdd">
                  <el-icon class="el-icon--left">
                    <Plus />
                  </el-icon>
                  添加
                </el-button>
              </el-col>
            </el-row>

            <el-row v-if="manualCoreTopics.length > 0" style="margin-bottom: 15px">
              <el-col :span="24">
                <el-form-item label="已选核心" label-width="80px">
                  <div>
                    <el-tag
                      v-for="topic in manualCoreTopics"
                      :key="`manual-core-${topic.originalIndex}`"
                      type="success"
                      closable
                      @close="removeCoreTopic(topic.originalIndex)"
                      style="margin-right: 8px; margin-bottom: 4px"
                    >
                      {{ topic.name }}
                    </el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-row v-if="localTopicSettings.selectionMode === 'random' && localTopicSettings.coreTopics.length > 0">
            <el-col :span="24">
              <el-form-item label="随机选中" label-width="80px">
                <div>
                  <el-tag
                    v-for="(topic, index) in localTopicSettings.coreTopics"
                    :key="index"
                    type="info"
                    style="margin-right: 8px; margin-bottom: 4px"
                  >
                    {{ topic.name }}
                  </el-tag>
                  <span class="el-upload__tip" style="line-height: normal; margin-left: 5px"
                    >(将基于这些话题从分类中随机选择)</span
                  >
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="localTopicSettings.coreTopics.length > 0">
            <el-row
              :gutter="20"
              v-for="(coreTopic, index) in localTopicSettings.coreTopics"
              :key="`final-topic-${index}`"
              style="margin-bottom: 15px; padding: 15px; border: 1px dashed #dcdfe6; border-radius: 4px"
            >
              <el-col :span="24">
                <div style="margin-bottom: 10px; font-weight: 500">
                  <span>最终话题 #{{ index + 1 }}</span>
                  <el-tag
                    size="small"
                    :type="coreTopic.source === 'random' ? 'info' : 'success'"
                    style="margin-left: 8px"
                  >
                    源: {{ coreTopic.name }}
                  </el-tag>
                  <el-tag
                    size="small"
                    :type="coreTopic.source === 'random' ? 'warning' : 'primary'"
                    style="margin-left: 5px"
                  >
                    {{ coreTopic.source === 'random' ? '随机' : '手动' }}
                  </el-tag>
                  <!-- 话题状态指示 -->
                  <el-tag size="small" type="success" style="margin-left: 5px"> 分类话题 </el-tag>
                </div>

                <el-form-item label-width="0">
                  <div style="display: flex; align-items: center; width: 100%">
                    <el-select
                      v-model="localTopicSettings.finalTopicGroups[index]"
                      multiple
                      :placeholder="`选择或添加话题 (最多5个)`"
                      :multiple-limit="5"
                      filterable
                      allow-create
                      default-first-option
                      :reserve-keyword="false"
                      style="flex-grow: 1; margin-right: 10px"
                    >
                      <el-option
                        v-for="topicOption in localTopicSettings.finalTopicOptions[index]"
                        :key="topicOption.challenge_info ? topicOption.challenge_info.cid : String(topicOption)"
                        :label="
                          topicOption.challenge_info ? topicOption.challenge_info.challenge_name : String(topicOption)
                        "
                        :value="topicOption.challenge_info ? topicOption.challenge_info.cha_name : String(topicOption)"
                      >
                        <div class="topic-option" v-if="topicOption.challenge_info">
                          <span>{{ topicOption.challenge_info.cha_name }}</span>
                        </div>
                        <span v-else>{{ String(topicOption) }}</span>
                      </el-option>
                      <el-option
                        v-for="customTopic in (localTopicSettings.customTopicGroups[index] || []).filter(
                          (ct) =>
                            !(localTopicSettings.finalTopicGroups[index] || []).includes(ct) &&
                            !(localTopicSettings.finalTopicOptions[index] || []).some(
                              (st) => st.challenge_info && st.challenge_info.cha_name === ct
                            )
                        )"
                        :key="'custom-' + index + '-' + customTopic"
                        :label="customTopic"
                        :value="customTopic"
                      >
                        <div class="topic-option">
                          <span>{{ customTopic }}</span>
                          <el-tag size="small" type="success">自定义</el-tag>
                        </div>
                      </el-option>
                    </el-select>
                    <el-button type="primary" link @click="showTopicInputDialog(index)" icon="Edit">手动编辑</el-button>
                    <el-button
                      v-if="coreTopic.source === 'manual'"
                      type="danger"
                      link
                      @click="removeCoreTopic(index)"
                      icon="Delete"
                      style="margin-left: 5px"
                      >移除源</el-button
                    >
                  </div>
                  <div class="el-upload__tip" style="line-height: 1.4; margin-top: 5px">
                    将为第
                    {{ index + 1 }}
                    个视频使用这些话题（最多5个）。这些话题来自所选分类的随机选择，您可以直接输入新话题按回车创建，或通过"手动编辑"按钮管理。
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </div>
      </div>
    </div>

    <!-- 购物车设置 -->
    <div class="form-item-container">
      <h3 class="section-title">购物车设置</h3>
      <div class="section-content">
        <el-alert
          title="选择商品后，发布视频的时候会自动设置该商品为购物车商品"
          type="warning"
          show-icon
          :closable="false"
          style="margin-bottom: 15px"
        />
        <el-form-item label="选择商品" label-width="80px">
          <el-select
            v-model="localSettings.manualProductId"
            placeholder="请选择商品"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option v-for="item in productOptions" :key="item.ID" :label="item.title" :value="item.ID">
              <div class="product-option" style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ item.title }}</span>
                <div style="display: flex; gap: 8px">
                  <el-tag size="small" type="info">带货标题: {{ item.promotionTitle || '-' }}</el-tag>
                  <el-tag size="small" type="info">佣金: {{ item.cosRatio }}%</el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </div>

    <!-- 话题编辑对话框 -->
    <el-dialog v-model="topicInputDialogVisible" title="手动编辑话题" width="500px" append-to-body destroy-on-close>
      <div class="custom-topic-input">
        <el-form :model="topicInputForm" ref="topicInputFormRef">
          <el-form-item>
            <div class="topic-input-tip">请输入话题 (源: {{ editingTopicSourceName }}), 每行一个，最多5个</div>
            <el-input
              type="textarea"
              v-model="topicInputForm.topics"
              :rows="5"
              placeholder="每行输入一个话题，不需要添加#号"
            ></el-input>
          </el-form-item>
          <div class="topic-preview" v-if="topicInputPreview.length > 0">
            <div class="preview-label">预览：</div>
            <div class="topic-tags-preview">
              <el-tag
                v-for="(topic, index) in topicInputPreview"
                :key="index"
                type="warning"
                size="small"
                class="topic-tag"
              >
                #{{ topic }}
              </el-tag>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="topicInputDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addCustomTopics">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    name: 'OtherSettings'
  }
</script>

<script setup>
  import { ref, reactive, watch, computed, nextTick, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
  import { getTopicList } from '@/api/media/topic'

  import { getProductManualList } from '@/api/douyin/product'

  // Props
  const props = defineProps({
    settings: {
      type: Object,
      required: true
    },
    selectedCategories: {
      type: Array,
      default: () => []
    },
    categoryOptions: {
      type: Array,
      default: () => []
    },
    topicSettings: {
      type: Object,
      required: true
    },
    topicCategoryOptions: {
      type: Array,
      default: () => []
    },
    topicOptions: {
      type: Array,
      default: () => []
    },
    manualTopicToAdd: {
      type: [String, Number],
      default: null
    }
  })

  // Emits
  const emit = defineEmits([
    'update:settings',
    'update:selectedCategories',
    'update:topicSettings',
    'update:topicOptions',
    'update:manualTopicToAdd'
  ])

  // Local reactive state
  const localSelectedCategories = ref([...props.selectedCategories])
  const localTopicSettings = reactive({ ...props.topicSettings })
  const localManualTopicToAdd = ref(props.manualTopicToAdd)
  const localSettings = reactive({
    videoTitle: '',
    ...props.settings
  })
  const productOptions = ref([])

  // Dialog state
  const topicInputDialogVisible = ref(false)
  const topicInputForm = ref({ topics: '' })
  const editingTopicIndex = ref(null)
  const editingTopicSourceName = ref('')

  // 防止循环更新的标志
  const isUpdatingFromParent = ref(false)

  // Watch for prop changes and update local state
  watch(
    () => props.selectedCategories,
    (newVal) => {
      if (!isUpdatingFromParent.value) {
        localSelectedCategories.value = [...newVal]
      }
    },
    { deep: true }
  )

  watch(
    () => props.topicSettings,
    (newVal) => {
      if (!isUpdatingFromParent.value) {
        Object.assign(localTopicSettings, newVal)
      }
    },
    { deep: true }
  )

  watch(
    () => props.manualTopicToAdd,
    (newVal) => {
      if (!isUpdatingFromParent.value) {
        localManualTopicToAdd.value = newVal
      }
    }
  )

  watch(
    () => props.settings,
    (newVal) => {
      if (!isUpdatingFromParent.value) {
        Object.assign(localSettings, newVal)
      }
    },
    { deep: true }
  )

  // Watch for local changes and emit updates
  watch(
    localSelectedCategories,
    (newVal) => {
      isUpdatingFromParent.value = true
      emit('update:selectedCategories', [...newVal])
      // 使用 nextTick 确保在下一个 tick 重置标志
      nextTick(() => {
        isUpdatingFromParent.value = false
      })
    },
    { deep: true }
  )

  watch(
    localSettings,
    (newVal) => {
      isUpdatingFromParent.value = true
      emit('update:settings', { ...newVal })
      nextTick(() => {
        isUpdatingFromParent.value = false
      })
    },
    { deep: true }
  )

  watch(
    localTopicSettings,
    (newVal) => {
      isUpdatingFromParent.value = true
      emit('update:topicSettings', { ...newVal })
      nextTick(() => {
        isUpdatingFromParent.value = false
      })
    },
    { deep: true }
  )

  watch(localManualTopicToAdd, (newVal) => {
    isUpdatingFromParent.value = true
    emit('update:manualTopicToAdd', newVal)
    nextTick(() => {
      isUpdatingFromParent.value = false
    })
  })

  // Computed
  const manualCoreTopics = computed(() => {
    return localTopicSettings.coreTopics
      .map((topic, index) => ({ ...topic, originalIndex: index }))
      .filter((topic) => topic.source === 'manual')
  })

  const topicInputPreview = computed(() => {
    if (!topicInputForm.value.topics) return []
    return topicInputForm.value.topics
      .split('\n')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0)
      .slice(0, 5)
  })

  // Methods
  const handleGenerateCountChange = (value) => {
    localSettings.generateCount = value
  }

  // Load topics by category
  const loadTopicsByCategory = async (categoryId) => {
    if (!categoryId) {
      emit('update:topicOptions', [])
      localTopicSettings.coreTopics = []
      return
    }
    try {
      const response = await getTopicList({ categoryId: categoryId, page: 1, pageSize: 999 })
      if (response.code === 0 && response.data && response.data.list) {
        const newTopicOptions = response.data.list.map((item) => ({
          id: item.ID,
          name: item.content
        }))
        emit('update:topicOptions', newTopicOptions)
      } else {
        emit('update:topicOptions', [])
        ElMessage.warning('获取话题列表失败: ' + (response.message || '未找到话题'))
      }
    } catch (error) {
      console.error('获取话题列表失败:', error)
      emit('update:topicOptions', [])
      ElMessage.warning('获取话题列表失败')
    }
  }

  const clearCoreTopicState = () => {
    localTopicSettings.coreTopics = []
    localTopicSettings.finalTopicGroups = []
    localTopicSettings.finalTopicOptions = []
    localTopicSettings.customTopicGroups = []
    localTopicSettings.searchStatus = []
    localManualTopicToAdd.value = null
    emit('update:topicOptions', [])
  }

  // Watch for topic category changes
  watch(
    () => localTopicSettings.categoryId,
    (newCategoryId, _oldCategoryId) => {
      clearCoreTopicState()

      if (!newCategoryId) {
        localTopicSettings.selectionMode = 'none'
        return
      }

      loadTopicsByCategory(newCategoryId).then(() => {
        if (localTopicSettings.selectionMode === 'random') {
          selectRandomTopic()
        }
      })
    }
  )

  // Watch for selection mode changes
  watch(
    () => localTopicSettings.selectionMode,
    (newMode) => {
      clearCoreTopicState()

      if (!localTopicSettings.categoryId) {
        return
      }

      loadTopicsByCategory(localTopicSettings.categoryId).then(() => {
        if (newMode === 'random') {
          selectRandomTopic()
        }
      })
    }
  )

  // Watch for core topics changes to auto-collapse when topics are generated
  watch(
    () => localTopicSettings.coreTopics.length,
    (newLength, oldLength) => {
      // 当有话题生成时（从0变为>0），自动收缩
      if (oldLength === 0 && newLength > 0) {
        isTopicExpanded.value = false
      }
    }
  )

  // Handle manual topic selection
  const addManualCoreTopic = () => {
    const topicId = localManualTopicToAdd.value
    if (!topicId) {
      ElMessage.warning('请先从下拉列表中选择一个话题')
      return
    }

    const generateCount = props.settings.generateCount || 1
    if (localTopicSettings.coreTopics.length >= generateCount) {
      ElMessage.warning(`最多只能添加 ${generateCount} 个核心话题`)
      return
    }

    const selectedTopic = props.topicOptions.find((t) => t.id === topicId)
    if (!selectedTopic || !selectedTopic.name) {
      ElMessage.error('选择的话题无效或未找到')
      return
    }

    const coreTopic = { name: selectedTopic.name, id: selectedTopic.id, source: 'manual' }

    localTopicSettings.coreTopics.push(coreTopic)
    localTopicSettings.finalTopicGroups.push([])
    localTopicSettings.finalTopicOptions.push([])
    localTopicSettings.customTopicGroups.push([])
    localTopicSettings.searchStatus.push('idle')

    localManualTopicToAdd.value = null

    generateRandomTopicsForIndex(localTopicSettings.coreTopics.length - 1)
  }

  const removeCoreTopic = (index) => {
    if (index < 0 || index >= localTopicSettings.coreTopics.length) return

    localTopicSettings.coreTopics.splice(index, 1)
    localTopicSettings.finalTopicGroups.splice(index, 1)
    localTopicSettings.finalTopicOptions.splice(index, 1)
    localTopicSettings.customTopicGroups.splice(index, 1)
    localTopicSettings.searchStatus.splice(index, 1)
  }

  // Select random topics
  const selectRandomTopic = async () => {
    const categoryId = localTopicSettings.categoryId
    const generateCount = props.settings.generateCount || 1

    clearCoreTopicState()

    if (!categoryId) {
      ElMessage.warning('请先选择一个话题分类')
      localTopicSettings.selectionMode = 'none'
      return
    }

    if (props.topicOptions.length === 0) {
      await loadTopicsByCategory(categoryId)
      if (props.topicOptions.length === 0) {
        ElMessage.warning('该分类下没有可用的话题')
        localTopicSettings.selectionMode = 'none'
        return
      }
    }

    const availableTopics = props.topicOptions
    const selectedCoreTopics = []
    const initialFinalTopicGroups = []
    const initialFinalTopicOptions = []
    const initialCustomTopicGroups = []
    const initialSearchStatus = []

    for (let i = 0; i < generateCount; i++) {
      const randomIndex = Math.floor(Math.random() * availableTopics.length)
      const randomTopic = availableTopics[randomIndex]

      if (randomTopic && randomTopic.name) {
        const coreTopic = { name: randomTopic.name, id: randomTopic.id, source: 'random' }
        selectedCoreTopics.push(coreTopic)
        initialFinalTopicGroups.push([])
        initialFinalTopicOptions.push([])
        initialCustomTopicGroups.push([])
        initialSearchStatus.push('idle')
      } else {
        selectedCoreTopics.push({ name: '选择失败', id: null, source: 'random' })
        initialFinalTopicGroups.push([])
        initialFinalTopicOptions.push([])
        initialCustomTopicGroups.push([])
        initialSearchStatus.push('error')
      }
    }

    localTopicSettings.coreTopics = selectedCoreTopics
    localTopicSettings.finalTopicGroups = initialFinalTopicGroups
    localTopicSettings.finalTopicOptions = initialFinalTopicOptions
    localTopicSettings.customTopicGroups = initialCustomTopicGroups
    localTopicSettings.searchStatus = initialSearchStatus

    // 为每个核心话题生成随机话题
    selectedCoreTopics.forEach((_, index) => {
      generateRandomTopicsForIndex(index)
    })
  }

  // 为指定索引生成随机话题
  const generateRandomTopicsForIndex = (index) => {
    if (props.topicOptions.length > 0) {
      const shuffledTopics = [...props.topicOptions].sort(() => 0.5 - Math.random())
      const countToPick = Math.min(5, shuffledTopics.length)
      const randomTopics = shuffledTopics.slice(0, countToPick).map((t) => t.name)

      if (!localTopicSettings.finalTopicGroups[index]) localTopicSettings.finalTopicGroups[index] = []
      if (!localTopicSettings.finalTopicOptions[index]) localTopicSettings.finalTopicOptions[index] = []
      if (!localTopicSettings.customTopicGroups[index]) localTopicSettings.customTopicGroups[index] = []

      localTopicSettings.finalTopicGroups[index] = randomTopics
      localTopicSettings.finalTopicOptions[index] = []
      localTopicSettings.customTopicGroups[index] = []
      localTopicSettings.searchStatus[index] = 'success'
    } else {
      // 生成通用备用话题
      const coreTopic = localTopicSettings.coreTopics[index]
      const coreTopicName = coreTopic ? coreTopic.name : ''
      const fallbackTopics = generateGenericFallbackTopics(coreTopicName)

      localTopicSettings.finalTopicGroups[index] = fallbackTopics
      localTopicSettings.finalTopicOptions[index] = []
      localTopicSettings.customTopicGroups[index] = []
      localTopicSettings.searchStatus[index] = 'success'
    }
  }

  // 生成通用备用话题
  const generateGenericFallbackTopics = (coreTopicName) => {
    const genericTopics = ['热门推荐', '每日分享', '生活小技巧', '实用干货', '精彩内容']

    // 如果有核心话题名称，将其作为第一个话题
    if (coreTopicName && coreTopicName.trim()) {
      return [coreTopicName.trim(), ...genericTopics.slice(0, 4)]
    }

    return genericTopics
  }

  // Show topic input dialog
  const showTopicInputDialog = (index) => {
    editingTopicIndex.value = index

    let currentTopics = []
    const finalGroup = localTopicSettings.finalTopicGroups[index] || []
    const customGroup = localTopicSettings.customTopicGroups[index] || []
    currentTopics = [...finalGroup.filter((t) => !customGroup.includes(t)), ...customGroup]
    editingTopicSourceName.value = localTopicSettings.coreTopics[index]?.name || `话题 #${index + 1}`

    topicInputForm.value.topics = currentTopics.join('\n')
    topicInputDialogVisible.value = true
  }

  const addCustomTopics = () => {
    const topics = topicInputForm.value.topics
      .split('\n')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0 && topic.length <= 50)

    if (topics.length > 5) {
      ElMessage.warning('最多只能添加5个话题')
      return
    }

    const finalTopics = [...new Set(topics)]
    const index = editingTopicIndex.value

    if (!localTopicSettings.customTopicGroups[index]) localTopicSettings.customTopicGroups[index] = []
    if (!localTopicSettings.finalTopicGroups[index]) localTopicSettings.finalTopicGroups[index] = []

    localTopicSettings.customTopicGroups[index] = finalTopics
    localTopicSettings.finalTopicGroups[index] = finalTopics

    editingTopicIndex.value = null
    topicInputDialogVisible.value = false
    ElMessage.success(`已${finalTopics.length > 0 ? '更新' : '清空'}话题 (源: ${editingTopicSourceName.value})`)
  }

  // Load product list
  const loadProductList = async () => {
    try {
      const response = await getProductManualList({ page: 1, pageSize: 999 })
      if (response.code === 0 && response.data && response.data.list) {
        productOptions.value = response.data.list
      } else {
        productOptions.value = []
        ElMessage.warning('获取商品列表失败: ' + (response.message || '未找到商品'))
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      productOptions.value = []
      ElMessage.warning('获取商品列表失败')
    }
  }

  // Load product list on component mount
  onMounted(() => {
    loadProductList()
  })

  // New state for topic expansion
  const isTopicExpanded = ref(true)

  // New method to toggle topic expansion
  const toggleTopicExpanded = () => {
    isTopicExpanded.value = !isTopicExpanded.value
  }
</script>

<style scoped>
  .form-item-container {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 6px;
    background-color: #fdfdfd;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    border-left: 4px solid #4b6cb7;
    padding-left: 10px;
  }

  .custom-topic-input {
    padding: 0px;
  }

  .topic-input-tip {
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
  }

  .topic-preview {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f8f8f8;
  }

  .preview-label {
    font-weight: 500;
    margin-bottom: 10px;
    color: #606266;
  }

  .topic-tags-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .topic-tags-preview .topic-tag {
    margin-bottom: 4px;
  }

  .topic-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .product-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
</style>
